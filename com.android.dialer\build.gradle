apply plugin: 'com.android.application'

android {
    namespace 'com.android.dialer'
    compileSdkVersion 35
    buildToolsVersion "34.0.0"

    defaultConfig {
        applicationId "com.android.dialer"
        minSdkVersion 21
        targetSdkVersion 35
        versionCode 71
        versionName "7.1"
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation 'junit:junit:4.13.2'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.annotation:annotation:1.7.1'
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.viewpager:viewpager:1.0.0'
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.1.0'
    implementation project(':com.android.phone.common')
    implementation project(':com.android.contacts.common')
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.legacy:legacy-support-v13:1.0.0'
    implementation 'androidx.fragment:fragment:1.6.2'
    implementation 'com.google.guava:guava:32.1.3-android'
    implementation 'com.umeng.analytics:analytics:latest.integration'
}

configurations {
    all {
        exclude group: 'com.google.guava', module: 'listenablefuture'
    }
}
