package com.google.common.base;

public final class MoreObjects {
    public static <T> T firstNonNull(T first, T second) {
        return first != null ? first : second;
    }
    
    public static ToStringHelper toStringHelper(Object self) {
        return new ToStringHelper(self.getClass().getSimpleName());
    }
    
    public static ToStringHelper toStringHelper(Class<?> clazz) {
        return new ToStringHelper(clazz.getSimpleName());
    }
    
    public static ToStringHelper toStringHelper(String className) {
        return new ToStringHelper(className);
    }
    
    public static class ToStringHelper {
        private final StringBuilder sb;
        
        private ToStringHelper(String className) {
            sb = new StringBuilder(className).append('{');
        }
        
        public ToStringHelper add(String name, Object value) {
            if (sb.charAt(sb.length() - 1) != '{') {
                sb.append(", ");
            }
            sb.append(name).append('=').append(value);
            return this;
        }
        
        @Override
        public String toString() {
            return sb.append('}').toString();
        }
    }
}