package com.google.common.base;

public final class Objects {
    public static boolean equal(Object a, Object b) {
        return java.util.Objects.equals(a, b);
    }
    
    public static int hashCode(Object... objects) {
        return java.util.Objects.hash(objects);
    }
    
    public static String toString(Object object) {
        return String.valueOf(object);
    }
    
    public static MoreObjects.ToStringHelper toStringHelper(Object self) {
        return MoreObjects.toStringHelper(self);
    }
}