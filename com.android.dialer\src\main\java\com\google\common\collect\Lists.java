package com.google.common.collect;

import java.util.ArrayList;
import java.util.Arrays;

public final class Lists {
    public static <E> ArrayList<E> newArrayList() {
        return new ArrayList<E>();
    }
    
    public static <E> ArrayList<E> newArrayList(E... elements) {
        ArrayList<E> list = new ArrayList<E>(elements.length);
        list.addAll(Arrays.asList(elements));
        return list;
    }
    
    public static <E> ArrayList<E> newArrayList(Iterable<? extends E> elements) {
        ArrayList<E> list = new ArrayList<E>();
        for (E element : elements) {
            list.add(element);
        }
        return list;
    }
    
    public static <E> ArrayList<E> newArrayListWithCapacity(int initialArraySize) {
        return new ArrayList<E>(initialArraySize);
    }
}